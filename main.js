import {LoginPage} from "./authentication/loginComponent.js"

document.addEventListener("DOMContentLoaded", async () => {
    var app = document.querySelector(".app")

        app.innerHTML = LoginPage()

        // Now dynamically load the login script after the DOM is ready
        let myscript=document.getElementById("dynamicscript");
        if(myscript)myscript.remove();


        const script = document.createElement('script')
        script.type = 'module'
        script.id="dynamicscript"
        script.src = './authentication/loginscript.js'
        document.head.appendChild(script)
        return
    

    
  
})