import {LoginPage} from "./authentication/loginComponent.js"
import {DashboardComponet} from "./Dashboard/dashboardPage.js"

window.onload = async () => {
    function loadScript(scriptUrl) {
        return new Promise((resolve, reject) => {
            let script = document.createElement("script");
            script.src = scriptUrl;
            script.defer = true;
            script.id = "dynamicScript";
            script.onload = resolve;
            script.onerror = reject;
            document.body.appendChild(script);
        });
    }

    // Define routes
    const routes = {
        "/": {
            render: () => LoginPage(),
            script: "./authentication/loginscript.js",
            css: "./authentication/loginStyle.css"
        },
        "/Dashboard": {
            render: () => DashboardComponet(),
            script: "./Dashboard/dashboardScript.js",
            css: ["./Dashboard/dashboardStyle.css"]
        }
    };

    function navigate(path) {
        history.pushState({}, "", path);
        renderPage();
    }

    async function renderPage() {
        let path = window.location.pathname;
        let app = document.querySelector(".app");

        if (routes[path]) {
            // Render the page content
            app.innerHTML = routes[path].render();

            // Remove existing script
            let existingScript = document.querySelector("#dynamicScript");
            if (existingScript) {
                document.body.removeChild(existingScript);
            }

            // Load page-specific script
            if (routes[path].script) {
                loadScript(routes[path].script)
                    .then(() => console.log(`${routes[path].script} loaded successfully`))
                    .catch(() => console.error(`Error loading ${routes[path].script}`));
            }

            // Remove existing CSS links
            let existingCssLinks = document.querySelectorAll(".dynamicCss");
            existingCssLinks.forEach(link => {
                document.head.removeChild(link);
            });

            // Load page-specific CSS
            if (routes[path].css) {
                if (Array.isArray(routes[path].css)) {
                    // Handle array of CSS files
                    routes[path].css.forEach((cssFile, index) => {
                        let cssLink = document.createElement("link");
                        cssLink.rel = "stylesheet";
                        cssLink.href = cssFile;
                        cssLink.className = "dynamicCss";
                        cssLink.id = `dynamicCss-${index}`;
                        document.head.appendChild(cssLink);
                        console.log(`${cssFile} loaded successfully`);
                    });
                } else {
                    // Handle single CSS file
                    let cssLink = document.createElement("link");
                    cssLink.rel = "stylesheet";
                    cssLink.href = routes[path].css;
                    cssLink.className = "dynamicCss";
                    cssLink.id = "dynamicCss-0";
                    document.head.appendChild(cssLink);
                    console.log(`${routes[path].css} loaded successfully`);
                }
            }
        } else {
            // Handle 404 - redirect to home
            window.location.href = "/";
        }
    }

    // Handle back/forward browser navigation
    window.onpopstate = renderPage;

    // Handle navigation for links
    document.addEventListener("click", (e) => {
        let target = e.target.closest("a");
        if (target && target.getAttribute("href") && target.getAttribute("href").startsWith("/")) {
            e.preventDefault();
            navigate(target.getAttribute("href"));
        }
    });

    // Initial page render
    renderPage();
};