<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>SVG Line Graph</title>
  <style>
    body {
      font-family: sans-serif;
      padding: 20px;
    }
    svg {
      width: 500px;
      height: 300px;
      border: 1px solid #ccc;
      background: #f9f9f9;
    }
    .axis {
      stroke: #333;
      stroke-width: 1;
    }
    .line {
      fill: none;
      stroke: steelblue;
      stroke-width: 2;
    }
    .dot {
      fill: steelblue;
    }
  </style>
</head>
<body>

<h2>SVG Line Graph with JavaScript</h2>

<svg id="chart" viewBox="0 0 500 300">
  <!-- Axes -->
  <line class="axis" x1="40" y1="10" x2="40" y2="260" />
  <line class="axis" x1="40" y1="260" x2="480" y2="260" />
  <!-- Line graph will go here -->
</svg>

<script>
  const data = [50, 90, 40, 120, 80, 60, 150]; // your Y values
  const svg = document.getElementById("chart");

  const maxY = Math.max(...data);
  const chartHeight = 250;
  const chartWidth = 440;
  const offsetX = 40;
  const offsetY = 10;

  const stepX = chartWidth / (data.length - 1);

  // Create polyline points
  let points = data.map((value, index) => {
    const x = offsetX + index * stepX;
    const y = offsetY + (chartHeight - (value / maxY) * chartHeight);
    return `${x},${y}`;
  }).join(" ");

  // Draw the polyline
  const polyline = document.createElementNS("http://www.w3.org/2000/svg", "polyline");
  polyline.setAttribute("points", points);
  polyline.setAttribute("class", "line");
  svg.appendChild(polyline);

  // Optional: Add dots at each point
  data.forEach((value, index) => {
    const x = offsetX + index * stepX;
    const y = offsetY + (chartHeight - (value / maxY) * chartHeight);
    const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
    circle.setAttribute("cx", x);
    circle.setAttribute("cy", y);
    circle.setAttribute("r", 4);
    circle.setAttribute("class", "dot");
    svg.appendChild(circle);
  });
</script>

</body>
</html>
