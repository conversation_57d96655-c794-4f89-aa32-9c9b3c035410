<!DOCTYPE html>
<html lang="en">
<head>
    <title>Progress Tracker Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src='https://kit.fontawesome.com/a076d05399.js' crossorigin='anonymous'></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .nav {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            width: 100%;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .user {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        #username {
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .user i {
            color: white;
            font-size: 1.5rem;
            background: rgba(255,255,255,0.2);
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .user i:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }
        
        .logout {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .logout:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .container {
            padding: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 0;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .cardHead {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            font-weight: 600;
            font-size: 1.1rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .cardHead::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .card:hover .cardHead::before {
            left: 100%;
        }
        
        .cardContent {
            padding: 25px;
            text-align: center;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        
        .cardContent .value {
            font-size: 2.5rem;
            font-weight: bold;
            color: black;
            margin: 10px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .cardContent .label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .graphs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .graph-container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .graph-container:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .graph-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        svg {
            width: 100%;
            height: auto;
        }
        
        .line-chart path {
            fill: none;
            stroke-width: 3;
            stroke-linecap: round;
            stroke-linejoin: round;
        }
        
        .xp-line {
            stroke: #4facfe;
        }
        
        .success-line {
            stroke: #4facfe;
        }
        
        .failed-line {
            stroke: #f5576c;
        }
        
        .data-point {
            stroke: white;
            stroke-width: 2;
            transition: all 0.3s ease;
        }
        
        .xp-point {
            fill: #4facfe;
        }
        
        .success-point {
            fill: #4facfe;
        }
        
        .failed-point {
            fill: #f5576c;
        }
        
        .data-point:hover {
            r: 6;
            transform: scale(1.2);
        }
        
        .grid-line {
            stroke: #e0e0e0;
            stroke-width: 1;
        }
        
        .axis-text {
            font-family: 'Segoe UI', sans-serif;
            font-size: 12px;
            fill: #666;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }
        
        @media (max-width: 768px) {
            .nav {
                padding: 15px 20px;
                flex-direction: column;
                gap: 15px;
            }
            
            .container {
                padding: 20px;
            }
            
            .graphs {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .cards {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .card, .graph-container {
            animation: fadeInUp 0.6s ease forwards;
        }
        
        .card:nth-child(2) {
            animation-delay: 0.1s;
        }
        
        .card:nth-child(3) {
            animation-delay: 0.2s;
        }
        
        .graph-container:nth-child(1) {
            animation-delay: 0.3s;
        }
        
        .graph-container:nth-child(2) {
            animation-delay: 0.4s;
        }
    </style>
</head>
<body>
    <nav class="nav">
        <div class="user">
            <span id="username">Teddy</span>
            <i class="fas fa-user"></i>
        </div>
        <div>
            <button class="logout">Log out</button>
        </div>
    </nav>
    
    <div class="container">
        <div class="cards">
            <div id="userIdentification" class="card">
                <div class="cardHead">
                    <span>User Profile</span>
                </div>
                <div class="cardContent">
                    <div><strong>Name:</strong> <span id="userName">Teddy</span></div>
                    <div><strong>Age:</strong> <span id="userAge">20</span></div>
                    <div class="label">Basic Information</div>
                </div>
            </div>
            
            <div id="userXp" class="card xp-progress">
                <div class="cardHead">
                    <span>XP Points</span>
                </div>
                <div class="cardContent">
                    <div class="value" id="totalXP">0</div>
                    <div class="label">Total XP Earned</div>
                </div>
            </div>
            
            <div id="userAudit" class="card audit-progress">
                <div class="cardHead">
                    <span>Audits Completed</span>
                </div>
                <div class="cardContent">
                    <div class="value" id="totalAudits">0</div>
                    <div class="label">Successful Audits</div>
                </div>
            </div>
        </div>
        
        <div class="graphs">
            <div class="graph-container">
                <div class="graph-title">XP Progress Over Time</div>
                <svg id="xpChart" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
                    <!-- Grid lines will be generated dynamically -->
                </svg>
            </div>
            
            <div class="graph-container">
                <div class="graph-title">Audit Performance Over Time</div>
                <svg id="auditChart" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
                    <!-- Chart will be generated dynamically -->
                </svg>
            </div>
        </div>
    </div>
    
    <script>
        // Dynamic data configuration
        const userData = {
            name: "Teddy",
            age: 20,
            xpData: [
                { month: "Jan", value: 50 },
                { month: "Feb", value: 120 },
                { month: "Mar", value: 200 },
                { month: "Apr", value: 280 },
                { month: "May", value: 350 },
                { month: "Jun", value: 400 }
            ],
            auditData: [
                { month: "Jan", successful: 1, failed: 1 },
                { month: "Feb", successful: 3, failed: 0 },
                { month: "Mar", successful: 1, failed: 1 },
                { month: "Apr", successful: 4, failed: 0 },
                { month: "May", successful: 3, failed: 0 },
                { month: "Jun", successful: 1, failed: 1 }
            ]
        };

        function handleLogout() {
            if (confirm('Are you sure you want to log out?')) {
                alert('Logging out... Redirecting to login page.');
                // In a real application, you would redirect to the login page
                // window.location.href = '/login';
            }
        }

        function createGrid(svg, maxY) {
            const gridGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
            gridGroup.setAttribute("class", "grid");
            
            // Horizontal grid lines
            for (let i = 0; i <= 5; i++) {
                const y = 40 + (i * 32);
                const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
                line.setAttribute("x1", "50");
                line.setAttribute("y1", y.toString());
                line.setAttribute("x2", "350");
                line.setAttribute("y2", y.toString());
                line.setAttribute("class", "grid-line");
                gridGroup.appendChild(line);
            }
            
            // Vertical grid lines
            for (let i = 0; i <= 6; i++) {
                const x = 50 + (i * 50);
                const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
                line.setAttribute("x1", x.toString());
                line.setAttribute("y1", "40");
                line.setAttribute("x2", x.toString());
                line.setAttribute("y2", "200");
                line.setAttribute("class", "grid-line");
                gridGroup.appendChild(line);
            }
            
            svg.appendChild(gridGroup);
            
            // Add axes
            const yAxis = document.createElementNS("http://www.w3.org/2000/svg", "line");
            yAxis.setAttribute("x1", "50");
            yAxis.setAttribute("y1", "40");
            yAxis.setAttribute("x2", "50");
            yAxis.setAttribute("y2", "200");
            yAxis.setAttribute("stroke", "#333");
            yAxis.setAttribute("stroke-width", "2");
            svg.appendChild(yAxis);
            
            const xAxis = document.createElementNS("http://www.w3.org/2000/svg", "line");
            xAxis.setAttribute("x1", "50");
            xAxis.setAttribute("y1", "200");
            xAxis.setAttribute("x2", "350");
            xAxis.setAttribute("y2", "200");
            xAxis.setAttribute("stroke", "#333");
            xAxis.setAttribute("stroke-width", "2");
            svg.appendChild(xAxis);
        }

        function createXPChart() {
            const svg = document.getElementById('xpChart');
            const maxXP = Math.max(...userData.xpData.map(d => d.value));
            
            // Clear existing content
            svg.innerHTML = '';
            
            // Add gradient definition
            const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
            const gradient = document.createElementNS("http://www.w3.org/2000/svg", "linearGradient");
            gradient.setAttribute("id", "areaGradient");
            gradient.setAttribute("x1", "0%");
            gradient.setAttribute("y1", "0%");
            gradient.setAttribute("x2", "0%");
            gradient.setAttribute("y2", "100%");
            
            const stop1 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
            stop1.setAttribute("offset", "0%");
            stop1.setAttribute("style", "stop-color:#4facfe;stop-opacity:0.3");
            
            const stop2 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
            stop2.setAttribute("offset", "100%");
            stop2.setAttribute("style", "stop-color:#4facfe;stop-opacity:0");
            
            gradient.appendChild(stop1);
            gradient.appendChild(stop2);
            defs.appendChild(gradient);
            svg.appendChild(defs);
            
            createGrid(svg, maxXP);
            
            // Y-axis labels
            for (let i = 0; i <= 5; i++) {
                const value = Math.round((maxXP / 5) * (5 - i));
                const y = 40 + (i * 32) + 5;
                const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
                text.setAttribute("x", "45");
                text.setAttribute("y", y.toString());
                text.setAttribute("class", "axis-text");
                text.setAttribute("text-anchor", "end");
                text.textContent = value.toString();
                svg.appendChild(text);
            }
            
            // X-axis labels and plot points
            let pathData = "M ";
            let areaPath = "M ";
            
            userData.xpData.forEach((data, index) => {
                const x = 50 + (index * 50);
                const y = 200 - ((data.value / maxXP) * 160);
                
                // X-axis label
                const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
                text.setAttribute("x", x.toString());
                text.setAttribute("y", "220");
                text.setAttribute("class", "axis-text");
                text.setAttribute("text-anchor", "middle");
                text.textContent = data.month;
                svg.appendChild(text);
                
                // Add to path
                if (index === 0) {
                    pathData += `${x} ${y}`;
                    areaPath += `${x} ${y}`;
                } else {
                    pathData += ` L ${x} ${y}`;
                    areaPath += ` L ${x} ${y}`;
                }
                
                // Data point
                const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
                circle.setAttribute("class", "data-point xp-point");
                circle.setAttribute("cx", x.toString());
                circle.setAttribute("cy", y.toString());
                circle.setAttribute("r", "4");
                svg.appendChild(circle);
            });
            
            // Close area path
            const lastX = 50 + ((userData.xpData.length - 1) * 50);
            areaPath += ` L ${lastX} 200 L 50 200 Z`;
            
            // Area under curve
            const areaElement = document.createElementNS("http://www.w3.org/2000/svg", "path");
            areaElement.setAttribute("d", areaPath);
            areaElement.setAttribute("fill", "url(#areaGradient)");
            svg.appendChild(areaElement);
            
            // Line
            const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
            path.setAttribute("class", "line-chart xp-line");
            path.setAttribute("d", pathData);
            svg.appendChild(path);
        }

        function createAuditChart() {
            const svg = document.getElementById('auditChart');
            // Calculate cumulative successful audits over time
            const cumulativeData = [];
            let cumulative = 0;
            
            userData.auditData.forEach((data, index) => {
                cumulative += data.successful;
                cumulativeData.push({
                    month: data.month,
                    value: cumulative
                });
            });
            
            const maxAudits = cumulativeData[cumulativeData.length - 1].value;
            
            // Clear existing content
            svg.innerHTML = '';
            
            // Add gradient definition (same as XP chart)
            const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
            const gradient = document.createElementNS("http://www.w3.org/2000/svg", "linearGradient");
            gradient.setAttribute("id", "auditAreaGradient");
            gradient.setAttribute("x1", "0%");
            gradient.setAttribute("y1", "0%");
            gradient.setAttribute("x2", "0%");
            gradient.setAttribute("y2", "100%");
            
            const stop1 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
            stop1.setAttribute("offset", "0%");
            stop1.setAttribute("style", "stop-color:#4facfe;stop-opacity:0.3");
            
            const stop2 = document.createElementNS("http://www.w3.org/2000/svg", "stop");
            stop2.setAttribute("offset", "100%");
            stop2.setAttribute("style", "stop-color:#4facfe;stop-opacity:0");
            
            gradient.appendChild(stop1);
            gradient.appendChild(stop2);
            defs.appendChild(gradient);
            svg.appendChild(defs);
            
            createGrid(svg, maxAudits);
            
            // Y-axis labels
            for (let i = 0; i <= 5; i++) {
                const value = Math.round((maxAudits / 5) * (5 - i));
                const y = 40 + (i * 32) + 5;
                const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
                text.setAttribute("x", "45");
                text.setAttribute("y", y.toString());
                text.setAttribute("class", "axis-text");
                text.setAttribute("text-anchor", "end");
                text.textContent = value.toString();
                svg.appendChild(text);
            }
            
            // X-axis labels and plot points
            let pathData = "M ";
            let areaPath = "M ";
            
            cumulativeData.forEach((data, index) => {
                const x = 50 + (index * 50);
                const y = 200 - ((data.value / maxAudits) * 160);
                
                // X-axis label
                const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
                text.setAttribute("x", x.toString());
                text.setAttribute("y", "220");
                text.setAttribute("class", "axis-text");
                text.setAttribute("text-anchor", "middle");
                text.textContent = data.month;
                svg.appendChild(text);
                
                // Add to path
                if (index === 0) {
                    pathData += `${x} ${y}`;
                    areaPath += `${x} ${y}`;
                } else {
                    pathData += ` L ${x} ${y}`;
                    areaPath += ` L ${x} ${y}`;
                }
                
                // Data point
                const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
                circle.setAttribute("class", "data-point success-point");
                circle.setAttribute("cx", x.toString());
                circle.setAttribute("cy", y.toString());
                circle.setAttribute("r", "4");
                svg.appendChild(circle);
            });
            
            // Close area path
            const lastX = 50 + ((cumulativeData.length - 1) * 50);
            areaPath += ` L ${lastX} 200 L 50 200 Z`;
            
            // Area under curve
            const areaElement = document.createElementNS("http://www.w3.org/2000/svg", "path");
            areaElement.setAttribute("d", areaPath);
            areaElement.setAttribute("fill", "url(#auditAreaGradient)");
            svg.appendChild(areaElement);
            
            // Line
            const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
            path.setAttribute("class", "line-chart success-line");
            path.setAttribute("d", pathData);
            svg.appendChild(path);
        }

        function updateUserData() {
            // Update user info
            document.getElementById('userName').textContent = userData.name;
            document.getElementById('userAge').textContent = userData.age;
            
            // Calculate totals
            const totalXP = userData.xpData[userData.xpData.length - 1].value;
            const totalSuccessfulAudits = userData.auditData.reduce((sum, data) => sum + data.successful, 0);
            
            // Update card values with animation
            animateValue('totalXP', 0, totalXP, 1500);
            setTimeout(() => {
                animateValue('totalAudits', 0, totalSuccessfulAudits, 1000);
            }, 500);
        }

        function animateValue(elementId, start, end, duration) {
            const element = document.getElementById(elementId);
            const startTime = performance.now();
            
            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const currentValue = Math.floor(start + (end - start) * progress);
                element.textContent = currentValue;
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }
            
            requestAnimationFrame(update);
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            updateUserData();
            createXPChart();
            createAuditChart();
        });
    </script>
</body>
</html>